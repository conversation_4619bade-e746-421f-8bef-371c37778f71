import _ from 'lodash';
import { flushSync } from 'react-dom';
import OpenAI from 'openai'; // OpenAI import might be redundant if getOpenAIClientForCalling handles it fully
import { isAbortError, generateUniqueId, corsFailedApiUrls, getOpenAIClientForCalling, parseThinkContent, extractReasoningFromChunk } from '../utils/index.js'; // Import new utils
import { streamTextWithAI, formatTextWithAI } from '../utils/ai.js'; // Import the new streamTextWithAI utility and formatTextWithAI
import { defaultTranslationPromptText } from '../utils/constant.js';

const generateContextSnippet = (fullText, termToFind, desiredLength = 200) => {
  if (!fullText || !termToFind) {
    // If fullText is available but termToFind is not, return start of fullText.
    // If fullText is not available, return empty string.
    return fullText ? (fullText.substring(0, desiredLength) + (fullText.length > desiredLength ? "..." : "")) : "";
  }

  const termLower = termToFind.toLowerCase();
  const textLower = fullText.toLowerCase();

  // Find the first occurrence of the term
  let termIndex = textLower.indexOf(termLower);

  if (termIndex === -1) {
    // Term not found, return beginning of the text as a fallback
    return fullText.substring(0, desiredLength) + (fullText.length > desiredLength ? "..." : "");
  }

  // ... rest of the function
  const termLength = termToFind.length; // Actual length of the term
  const contextToEachSide = Math.floor((desiredLength - termLength) / 2);

  let startIndex = Math.max(0, termIndex - contextToEachSide);
  let endIndex = Math.min(fullText.length, termIndex + termLength + contextToEachSide);
  
  // Attempt to make the snippet closer to desiredLength if possible
  const currentSnippetLength = endIndex - startIndex;
  if (currentSnippetLength < desiredLength && currentSnippetLength > 0) {
    const diff = desiredLength - currentSnippetLength;
    const halfDiffCeil = Math.ceil(diff / 2);
    const halfDiffFloor = Math.floor(diff / 2);

    if (startIndex === 0 && endIndex < fullText.length) { // Can only expand to the right
        endIndex = Math.min(fullText.length, endIndex + diff);
    } else if (endIndex === fullText.length && startIndex > 0) { // Can only expand to the left
        startIndex = Math.max(0, startIndex - diff);
    } else if (startIndex > 0 && endIndex < fullText.length) { // Can expand both sides
        endIndex = Math.min(fullText.length, endIndex + halfDiffCeil);
        startIndex = Math.max(0, startIndex - halfDiffFloor);
    }
  }
  
  // Final boundary check and ensure snippet length is close to desiredLength if possible
  // Recalculate if expansion was uneven or limited
    if (endIndex - startIndex < desiredLength) {
        if (startIndex === 0) {
            endIndex = Math.min(fullText.length, startIndex + desiredLength);
        } else if (endIndex === fullText.length) {
            startIndex = Math.max(0, endIndex - desiredLength);
        }
    }
    // If it's still too long due to short fullText and aggressive expansion.
    if (endIndex - startIndex > desiredLength) {
        if (termIndex - Math.floor((desiredLength - termLength) / 2) < 0 ) { // term is near start
            startIndex = 0;
            endIndex = Math.min(fullText.length, desiredLength);
        } else if (termIndex + termLength + Math.ceil((desiredLength - termLength) / 2) > fullText.length) { // term is near end
            endIndex = fullText.length;
            startIndex = Math.max(0, fullText.length - desiredLength);
        } else { // term is in middle, re-center
            startIndex = Math.max(0, termIndex - Math.floor((desiredLength - termLength) / 2));
            endIndex = Math.min(fullText.length, startIndex + desiredLength);
        }
    }
   // Ensure startIndex is not negative and endIndex does not exceed fullText.length
  startIndex = Math.max(0, startIndex);
  endIndex = Math.min(fullText.length, endIndex);

  let snippet = fullText.substring(startIndex, endIndex);
  let prefix = "";
  let suffix = "";

  if (startIndex > 0) {
    prefix = "...";
  }
  if (endIndex < fullText.length) {
    suffix = "...";
  }

  // Prevent adding "..." if the snippet itself starts/ends with "..." due to being at actual text boundary
  if (snippet.startsWith("...") && prefix === "...") { // Check actual snippet content
      prefix = "";
  }
  if (snippet.endsWith("...") && suffix === "...") { // Check actual snippet content
      suffix = "";
  }

  const finalSnippet = prefix + snippet + suffix;
  return finalSnippet;
};
// 辅助函数，用于更新流式翻译内容 (假设 currentTranslated 是对象, index 是 item.id)
export const updateStreamedTranslation = (itemId, delta, currentTranslatedObject) => {
  const newTranslatedObject = { ...currentTranslatedObject };
  if (!newTranslatedObject[itemId] || typeof newTranslatedObject[itemId] !== 'object' || newTranslatedObject[itemId].type !== 'text') {
    // Initialize or re-initialize if not a text object
    // This might need more context on how placeholder/img objects are handled during streaming
    newTranslatedObject[itemId] = { type: 'text', children: '' };
  }
  // Ensure children is a string before appending
  if (typeof newTranslatedObject[itemId].children !== 'string') {
    newTranslatedObject[itemId].children = '';
  }
  newTranslatedObject[itemId].children += delta;
  return newTranslatedObject;
};

// 翻译相关的初始状态
const translationState = {
  streamingChunkRawTexts: new Map(), // Map<number, string> 每个区块的原始流输出临时状态
  currentTranslationControllers: new Map(), // Map<number, { controller: AbortController, streamEnded: boolean }> 每个区块的活动控制器和流结束标志
  isTranslationGloballyCancelled: false, // 停止 translateAll 队列处理的标志
  isTranslatingAllActive: false, // 指示 'translateAll' 过程是否正在运行的标志
  preTranslationChunkState: new Map(), // Map<number, Map<number, object | undefined>> 存储翻译前的状态以便回滚
  indicesToTranslate: [], // 全文翻译时需要翻译的区块索引
  chunkThinkContents: new Map(), // Map<number, string> 每个区块的think过程内容
  chunkThinkStates: new Map(), // Map<number, { isThinking: boolean, isCompleted: boolean }> 每个区块的think状态
  isTerminologyModalOpen: false, // 控制术语统一模态框的显示/隐藏
  terminologyListForModal: [], // 存储待审核的术语列表数据
  // 新增状态，用于存储AI解析出的术语的原始上下文信息
  aiParsedTerminologyContext: new Map(), // Map<originalTerm, { variantsInChunks: [{ chunkIndex, translationVariant, originalChunkContent, translatedChunkContent }]}>
  // 术语提取进度状态
  terminologyExtractionProgress: {
    isProcessing: false,
    currentStep: '',
    processedChunks: 0,
    totalChunks: 0,
    errors: []
  },
};

// 创建翻译状态管理函数
const createTranslationStateManagement = (set, get) => ({
  setStreamingChunkText: (chunkIndex, text) => set(state => {
    const newMap = new Map(state.streamingChunkRawTexts);
    newMap.set(chunkIndex, text);
    return { streamingChunkRawTexts: newMap };
  }),
  clearStreamingChunkText: (chunkIndex) => set(state => {
    const newMap = new Map(state.streamingChunkRawTexts);
    newMap.delete(chunkIndex);
    return { streamingChunkRawTexts: newMap };
  }),
  clearAllStreamingTexts: () => set({ streamingChunkRawTexts: new Map() }),

  addTranslationController: (chunkIndex, controller) => set(state => {
    const newMap = new Map(state.currentTranslationControllers);
    // Roo: 当添加新的 controller 时，初始化 streamEnded 为 false
    newMap.set(chunkIndex, { controller: controller, streamEnded: false });
    return { currentTranslationControllers: newMap };
  }),
  removeTranslationController: (chunkIndex) => set(state => {
    const newMap = new Map(state.currentTranslationControllers);
    newMap.delete(chunkIndex);
    return { currentTranslationControllers: newMap };
  }),
  clearAllControllers: () => set({ currentTranslationControllers: new Map() }),

  setGlobalCancellationFlag: (value) => set({ isTranslationGloballyCancelled: value }),
  setIsTranslatingAllActive: (value) => set({ isTranslatingAllActive: value }),

  setPreTranslationChunkState: (chunkIndex, stateMap) => set(state => {
    const newMap = new Map(state.preTranslationChunkState);
    newMap.set(chunkIndex, stateMap);
    return { preTranslationChunkState: newMap };
  }),
  clearPreTranslationChunkState: (chunkIndex) => set(state => {
    const newMap = new Map(state.preTranslationChunkState);
    newMap.delete(chunkIndex);
    return { preTranslationChunkState: newMap };
  }),

  setIndicesToTranslate: (indices) => set({ indicesToTranslate: indices }),
  clearIndicesToTranslate: () => set({ indicesToTranslate: [] }),

  // Think 相关状态管理
  setChunkThinkContent: (chunkIndex, content) => set(state => {
    const newMap = new Map(state.chunkThinkContents);
    newMap.set(chunkIndex, content);
    return { chunkThinkContents: newMap };
  }),
  clearChunkThinkContent: (chunkIndex) => set(state => {
    const newMap = new Map(state.chunkThinkContents);
    newMap.delete(chunkIndex);
    return { chunkThinkContents: newMap };
  }),
  setChunkThinkState: (chunkIndex, thinkState) => set(state => {
    const newMap = new Map(state.chunkThinkStates);
    newMap.set(chunkIndex, thinkState);
    return { chunkThinkStates: newMap };
  }),
  clearChunkThinkState: (chunkIndex) => set(state => {
    const newMap = new Map(state.chunkThinkStates);
    newMap.delete(chunkIndex);
    return { chunkThinkStates: newMap };
  }),
  clearAllThinkStates: () => set({
    chunkThinkContents: new Map(),
    chunkThinkStates: new Map()
  }),
  setAiParsedTerminologyContext: (contextMap) => set({ aiParsedTerminologyContext: contextMap }),
  clearAiParsedTerminologyContext: () => set({ aiParsedTerminologyContext: new Map() }),
});

// 当流式文本更新时，分发自定义事件
const dispatchStreamUpdate = (chunkIndex, text) => {
  if (typeof window !== 'undefined') {
    const timestamp = new Date().toISOString();
    const eventId = `${timestamp}-${chunkIndex}-${Math.random().toString(36).substring(2, 9)}`;

    window.dispatchEvent(new CustomEvent('stream-translation-update', {
      detail: { chunkIndex, text, eventId, timestamp }
    }));
  }
};

// 创建翻译服务，包含使用 API 翻译单个区块的核心逻辑
const createTranslationService = (set, get) => ({
  translateChunk: async (chunk, chunkIndex, retryCount = 0, isViaProxy = false) => { // 添加 isViaProxy 参数
    let hasBeenCancelled = false; // 跟踪翻译是否被取消
    const { content, removeTranslationController } = get();
    const isManualTranslation = !get().isTranslatingAllActive;

    const lastItemInChunk = chunk && chunk.length > 0 ? chunk[chunk.length - 1] : null;
    const lastOriginalIdInChunk = lastItemInChunk ? String(lastItemInChunk.id) : null;
    const currentActiveControllers = get().currentTranslationControllers;

    // Only block if it's a manual translation, it's the *initial* attempt (not a proxy retry), AND a controller already exists.
    if (isManualTranslation && !isViaProxy && currentActiveControllers.has(chunkIndex)) {
      return { status: 'already_translating' };
    }

    let rafHandle = null;
    let lastUpdateTime = Date.now();
    const MIN_UPDATE_INTERVAL = 300; // 毫秒

    const {
      apis, defaultApiModel, translationPrompt, appendGlossaryToPrompt,
      addTranslationController,
      setPreTranslationChunkState, clearPreTranslationChunkState,
    } = get();

    let provider, model, selectedApi;

    if (defaultApiModel && defaultApiModel[0] && defaultApiModel[1]) {
      [provider, model] = defaultApiModel;
      if (provider === '系统默认') {
        selectedApi = {
          key: 'system-default',
          provider: '系统默认',
          apiUrl: '/api/v1/chat/completions',
          apiKey: '',
          models: ['GLM-4-9B-0414'],
        };
        if (!selectedApi.models.includes(model)) {
            model = selectedApi.models[0];
        }
      } else {
        selectedApi = apis.find(api => api.provider === provider);
      }
    } else {
      provider = '系统默认';
      model = 'GLM-4-9B-0414';
      selectedApi = {
        key: 'system-default',
        provider: '系统默认',
        apiUrl: '/api/v1/chat/completions',
        apiKey: '',
        models: ['GLM-4-9B-0414'],
      };
      get().setDefaultApiModel(provider, model);
    }

    if (!selectedApi) {
      removeTranslationController(chunkIndex); // This might be called before controller is added if error occurs early
      const errorMsg = provider === '系统默认' ? '系统默认API配置未能正确加载，请检查代码。' : `未找到提供商 "${provider}" 的有效 API 配置。`;
      console.error(`[TranslationStore translateChunk] API config error for chunkIndex ${chunkIndex}: ${errorMsg}. SelectedApi was falsy.`);
      throw new Error(errorMsg);
    }


    if (selectedApi.key !== 'system-default' && (!selectedApi.apiUrl || !selectedApi.apiKey)) {
      removeTranslationController(chunkIndex);
      throw new Error(`未找到提供商 "${provider}" 的有效 API 配置 (URL 或 Key 缺失)`);
    }
     if (selectedApi.key === 'system-default' && !selectedApi.apiUrl) {
      removeTranslationController(chunkIndex);
      throw new Error(`“系统默认”API 配置不完整 (URL 缺失)`);
    }

    if (!selectedApi.models || !selectedApi.models.includes(model)) {
      removeTranslationController(chunkIndex);
      throw new Error(`提供商 "${provider}" 不支持模型 "${model}" 或模型列表未定义`);
    }

    let combinedText = '';
    const translationMap = [];
    const separator = "\n";
    chunk.forEach(itemInChunk => {
      const itemId = itemInChunk.id;
      if (itemId === undefined || itemId === null) return;

      const originalTag = itemInChunk.tag;

      if (originalTag === 'img') {
        translationMap.push({ itemId, type: 'img', originalItem: itemInChunk, originalTag });
      } else if (originalTag === 'table') {
        const tableHtml = itemInChunk.children || '';
        if (tableHtml && tableHtml.trim() !== '') {
          combinedText += `${tableHtml}${separator}`;
          translationMap.push({ itemId, type: 'table', originalTag, originalItem: itemInChunk, originalHtml: tableHtml });
        } else {
          translationMap.push({ itemId, type: 'empty-table', originalTag, originalItem: itemInChunk });
        }
      } else {
        const text = itemInChunk.children || '';
        if (typeof text === 'string' && text.trim()) {
          combinedText += text + separator;
          translationMap.push({ itemId, type: 'text', originalTag, originalItem: itemInChunk });
        } else {
          translationMap.push({ itemId, type: 'passthrough', originalTag, originalItem: itemInChunk });
        }
      }
    });

    if (combinedText.endsWith(separator)) {
      combinedText = combinedText.slice(0, -separator.length);
    }

    const itemsToTranslateCount = translationMap.filter(item => item.type === 'text' || item.type === 'table').length;
    if (itemsToTranslateCount === 0) {
      const currentTranslatedObject = { ...(get().translated || {}) };
      let changed = false;
      translationMap.forEach(mapEntry => {
        const itemId = mapEntry.itemId;
        const originalItem = mapEntry.originalItem;
        if (itemId !== undefined && originalItem) {
          if (!_.isEqual(currentTranslatedObject[itemId], originalItem)) {
            currentTranslatedObject[itemId] = { ...originalItem };
            changed = true;
          }
        }
      });
      if (changed) {
        set({ translated: currentTranslatedObject });
      }
      removeTranslationController(chunkIndex);
      return { status: 'no_content' };
    }

    // OpenAI client initialization and direct API call logic will be handled by streamTextWithAI

    const basePrompt = get().translationPrompt || defaultTranslationPromptText;
    const finalSystemPrompt = appendGlossaryToPrompt(basePrompt, 'translation');

    // **FIXED**: Initialize initialStreamingStateObject scoped to the current chunk.
    const initialStreamingStateObject = {};
    const currentChunkOriginalIds = chunk.map(item => item.id).filter(id => id !== undefined);
    const globalTranslated = get().translated || {};
    currentChunkOriginalIds.forEach(id => {
      if (globalTranslated.hasOwnProperty(String(id))) { // Ensure string comparison for keys if necessary
        initialStreamingStateObject[String(id)] = _.cloneDeep(globalTranslated[String(id)]);
      }
    });

    translationMap.forEach((mapEntry) => {
      const itemId = mapEntry.itemId;
      const originalItem = mapEntry.originalItem;
      if (itemId === undefined || !originalItem) return;

      const itemIdStr = String(itemId); // Ensure consistent key type

      if (mapEntry.type === 'img' || mapEntry.type === 'passthrough') {
        initialStreamingStateObject[itemIdStr] = { ...originalItem };
      } else if (mapEntry.type === 'text') {
        initialStreamingStateObject[itemIdStr] = { id: itemId, tag: originalItem.tag, type: 'text', children: '' };
      } else if (mapEntry.type === 'table') {
        initialStreamingStateObject[itemIdStr] = { tag: 'table', id: itemId, children: `<div style="padding:5px;text-align:center;border:1px dotted #ccc;">[表格翻译中...]</div>` };
      } else if (mapEntry.type === 'empty-table') {
        initialStreamingStateObject[itemIdStr] = { ...originalItem, children: '' };
      }
    });

    set(state => {
        const newTranslatedState = { ...(state.translated || {}) };
        let changed = false;
        translationMap.forEach(mapEntry => { // translationMap IDs are from the current chunk
            const itemId = mapEntry.itemId;
            if (itemId !== undefined) {
                const itemIdStr = String(itemId);
                if (initialStreamingStateObject.hasOwnProperty(itemIdStr)) { // Ensure the item was set in initialStreamingStateObject
                    if (!_.isEqual(newTranslatedState[itemIdStr], initialStreamingStateObject[itemIdStr])) {
                        newTranslatedState[itemIdStr] = initialStreamingStateObject[itemIdStr];
                        changed = true;
                    }
                }
            }
        });
        return changed ? { translated: newTranslatedState } : {};
      });

    const controller = new AbortController();
    const signal = controller.signal;
    signal.addEventListener('abort', () => {
      hasBeenCancelled = true;
    });
    addTranslationController(chunkIndex, controller);


    const preTranslationStateMap = new Map();
    chunk.forEach(item => {
      if (item && typeof item.id !== 'undefined') {
        preTranslationStateMap.set(String(item.id), (get().translated || {})[String(item.id)]);
      }
    });
    setPreTranslationChunkState(chunkIndex, preTranslationStateMap);

    let rafPending = false;
    let pendingStateUpdates = new Map(); // 批量状态更新缓存

    function scheduleSetTranslated(currentStreamingStateObjectForRaf) { // Renamed to avoid confusion
      if (!rafPending) {
        rafPending = true;
        requestAnimationFrame(() => {
          const itemsInCurrentChunk = chunk;
          const translatedItemsForThisChunkForDispatch = itemsInCurrentChunk.map(originalItem => {
            if (originalItem && typeof originalItem.id !== 'undefined') {
              return currentStreamingStateObjectForRaf[String(originalItem.id)];
            }
            return undefined;
          }).filter(item => item !== undefined);

          dispatchStreamUpdate(chunkIndex, translatedItemsForThisChunkForDispatch);

          if (Date.now() - lastUpdateTime > MIN_UPDATE_INTERVAL) {
            // 批量更新状态，避免频繁的单独更新
            const batchUpdate = () => {
              set(state => {
                const newStoreTranslated = { ...(state.translated || {}) };
                let changed = false;
                const controllerEntry = get().currentTranslationControllers.get(chunkIndex);
                // const isStreamEndedForChunk = controllerEntry?.streamEnded; // Not directly used in this logic path anymore

                Object.keys(currentStreamingStateObjectForRaf).forEach(key => {
                  if(initialStreamingStateObject.hasOwnProperty(key)) { // Ensure we only update items belonging to this chunk
                      // Roo: Removed the condition that skipped updating the last item during streaming in RAF.
                      // if (key === lastOriginalIdInChunk) {
                      //   // console.log(`[Store RAF] Chunk ${chunkIndex}, ItemID ${key} (LastItem) - SKIPPING update in RAF. Finally block handles it.`);
                      //   return;
                      // }
                      if (!_.isEqual(newStoreTranslated[key], currentStreamingStateObjectForRaf[key])) {
                          newStoreTranslated[key] = currentStreamingStateObjectForRaf[key];
                          changed = true;
                      }
                  }
                });
                return changed ? { translated: newStoreTranslated } : {};
              });
            };

            // 使用防抖机制避免过于频繁的状态更新
            if (pendingStateUpdates.has(chunkIndex)) {
              clearTimeout(pendingStateUpdates.get(chunkIndex));
            }

            pendingStateUpdates.set(chunkIndex, setTimeout(() => {
              batchUpdate();
              pendingStateUpdates.delete(chunkIndex);
              lastUpdateTime = Date.now();
            }, 16)); // 16ms 防抖，约60fps
          }
          rafPending = false;
        });
      }
    }

    let accumulatedStreamedText = '';
    let currentTranslatableMapIndex = 0;
    let currentPartAccumulator = '';
    let streamingTranslatedStateObject = { ...initialStreamingStateObject }; // **FIXED**: Now correctly scoped
    const separatorChar = '\n';

    // Think 过程相关变量
    // const isThinkModelUsed = isThinkModel(model); // Removed: No longer needed
    const { setChunkThinkContent, setChunkThinkState, clearChunkThinkContent } = get();
    let accumulatedReasoning = '';
    let hasStartedFormalContent = false; // 标记是否已开始输出正式内容

    // Think状态更新防抖
    let thinkUpdateTimeout = null;
    const batchThinkUpdate = (content, state) => {
      if (thinkUpdateTimeout) {
        clearTimeout(thinkUpdateTimeout);
      }
      thinkUpdateTimeout = setTimeout(() => {
        if (content !== undefined) {
          setChunkThinkContent(chunkIndex, content);
        }
        if (state !== undefined) {
          setChunkThinkState(chunkIndex, state);
        }
      }, 50); // 50ms 防抖
    };

    console.log(`[Translation] Chunk ${chunkIndex}, Model: ${model}`); // Removed isThinkModelUsed from log

    // 初始化 think 状态 (无条件)
    setChunkThinkState(chunkIndex, { isThinking: false, isCompleted: false });
    clearChunkThinkContent(chunkIndex);

    try {
      const userMessageContent = combinedText;
      const tableItems = translationMap.filter(mapEntry => mapEntry.type === 'table');
      const tableAccumulators = {};
      tableItems.forEach(mapEntry => { tableAccumulators[String(mapEntry.itemId)] = ''; });
      let currentTableProcessingIndex = tableItems.length > 0 ? translationMap.indexOf(tableItems[0]) : -1;

      // Define the onChunk callback for streamTextWithAI
      const handleStreamChunk = (chunkData) => {
        if (signal.aborted) {
          return false;
        }

        const { content: deltaContent, reasoning, error: streamError, isFinal } = chunkData;

        if (streamError) {
          console.error(`[Translation] Stream error for chunk ${chunkIndex}: ${streamError}`);
          return false; // Stop processing on stream error
        }

        if (isFinal) {
          return;
        }
        
        // 提取 reasoning 和 content (reasoning is already extracted by streamTextWithAI if pattern matches)
        // const { reasoning, content: deltaContent } = extractReasoningFromChunk(rawChunk); // rawChunk is no longer directly available here

        // 调试信息
        // if (reasoning || deltaContent) {
          // console.log(`[Translation] Chunk ${chunkIndex}, reasoning: "${reasoning}", content: "${deltaContent}"`);
        // }

        // 处理 reasoning 字段
        if (reasoning) { // Removed isThinkModelUsed condition
          accumulatedReasoning += reasoning;
          batchThinkUpdate(accumulatedReasoning, { isThinking: true, isCompleted: false });
        }

        // 处理 content 字段
        if (!deltaContent) {
          return true; // Continue stream if no content in this chunk
        }

        accumulatedStreamedText += deltaContent;

        // 当开始有正式内容输出时（基于reasoning字段的模型），立即标记think完成
        if (deltaContent.trim() && !hasStartedFormalContent && reasoning) { // Removed isThinkModelUsed condition
          hasStartedFormalContent = true;
          batchThinkUpdate(undefined, { isThinking: false, isCompleted: true });
        }

        // 使用增强的parseThinkContent函数，同时处理<think>标签和reasoning格式
        const parsed = parseThinkContent(accumulatedStreamedText, accumulatedReasoning); // Use accumulated text and reasoning

          // 如果有 think 内容，更新 think 状态
          if (parsed.thinkBlocks.length > 0) {
            const latestThink = parsed.thinkBlocks[parsed.thinkBlocks.length - 1];
            // 优先显示<think>标签内容，如果没有则显示reasoning内容
            if (latestThink.type === 'think-tag' || (latestThink.type === 'reasoning' && !parsed.thinkBlocks.some(block => block.type === 'think-tag'))) {
              batchThinkUpdate(latestThink.content, { isThinking: true, isCompleted: false });
            }
          }

          // 检查思考过程是否完成（基于think标签）
          if (parsed.isThinkProcessComplete && !hasStartedFormalContent) {
            hasStartedFormalContent = true;
            batchThinkUpdate(undefined, { isThinking: false, isCompleted: true });
          }

          // 对于reasoning格式，当有正式内容输出时标记think完成
          if (parsed.cleanContent.trim() && !hasStartedFormalContent && accumulatedReasoning) {
            hasStartedFormalContent = true;
            batchThinkUpdate(undefined, { isThinking: false, isCompleted: true });
          }
          // The currentPartAccumulator should be built from the clean content.
          // Since we parse `accumulatedStreamedText`, let's rebuild `currentPartAccumulator` from `parsed.cleanContent`
          // This is a simplification; a more robust way would be to diff `parsed.cleanContent` from its previous state.
          // For now, let's assume deltaContent is what needs to be added to currentPartAccumulator if it's not part of a think tag.
          // If it *was* part of a think tag, it shouldn't be added.
          // This logic is complex to replicate perfectly from the original loop.
          // A simpler approach: `streamTextWithAI` could optionally return cleaned content if it detects think tags.
          // Or, the `onChunk` receives raw delta, and this function does the cleaning.
          // Let's stick to adding raw deltaContent to currentPartAccumulator for now, and the splitting logic will handle it.
          // The `parseThinkContent` on `accumulatedStreamedText` is the primary source for clean content.
        // } // End of removed "if (isThinkModelUsed)"
        
        currentPartAccumulator += deltaContent; // Add raw delta, splitting logic handles it

        if (currentTableProcessingIndex !== -1 && translationMap[currentTableProcessingIndex]?.type === 'table') {
            const tableMapEntry = translationMap[currentTableProcessingIndex];
            const tableItemId = tableMapEntry.itemId;
            const tableItemIdStr = String(tableItemId);
            if (tableItemId !== undefined) {
                tableAccumulators[tableItemIdStr] = (tableAccumulators[tableItemIdStr] || '') + deltaContent;
                let currentTableHtml = tableAccumulators[tableItemIdStr] || '';
                const newTableItem = {
                    tag: 'table',
                    id: tableItemId,
                    children: currentTableHtml.trim() ? currentTableHtml : `<div style="padding:5px;text-align:center;border:1px dotted #ccc;">[表格翻译中...]</div>`
                };
                if (!_.isEqual(streamingTranslatedStateObject[tableItemIdStr], newTableItem)) {
                    streamingTranslatedStateObject[tableItemIdStr] = newTableItem;
                }
                scheduleSetTranslated(streamingTranslatedStateObject);
            }
        }

        if (currentPartAccumulator.includes(separatorChar)) {
          const parts = currentPartAccumulator.split(separatorChar);
          const completeParts = parts.slice(0, -1);
          currentPartAccumulator = parts[parts.length - 1] || '';

          for (const completedPart of completeParts) {
            if (completedPart === '') continue; // Skip empty parts that might result from double separators
            
            // Always parse the completedPart to remove any think tags
            let partToTranslate = parseThinkContent(completedPart).cleanContent; // Removed isThinkModelUsed condition
            // if (isThinkModelUsed) {
            //     partToTranslate = parseThinkContent(completedPart).cleanContent;
            // }
            if (partToTranslate === '') continue; // Skip if cleaned part is empty

            while (currentTranslatableMapIndex < translationMap.length &&
                   !(translationMap[currentTranslatableMapIndex].type === 'text' || translationMap[currentTranslatableMapIndex].type === 'table')) {
              currentTranslatableMapIndex++;
            }
            if (currentTranslatableMapIndex < translationMap.length) {
              const mapEntry = translationMap[currentTranslatableMapIndex];
              const itemId = mapEntry.itemId;
              const originalItem = mapEntry.originalItem;
              const itemIdStr = String(itemId);

              if (itemId !== undefined && originalItem) {
                if (mapEntry.type === 'text') {
                  const newTextItem = {
                    ...(initialStreamingStateObject[itemIdStr] || { id: itemId, tag: originalItem.tag, type: 'text' }),
                    children: partToTranslate, // Use potentially cleaned part
                    type: 'text',
                    tag: originalItem.tag,
                    id: itemId
                  };
                  if (!_.isEqual(streamingTranslatedStateObject[itemIdStr], newTextItem)) {
                      streamingTranslatedStateObject[itemIdStr] = newTextItem;
                  }
                  scheduleSetTranslated(streamingTranslatedStateObject);
                } else if (mapEntry.type === 'table') {
                  tableAccumulators[itemIdStr] = partToTranslate; // Use potentially cleaned part
                  const newTableItem = {
                    tag: 'table',
                    id: itemId,
                    children: partToTranslate.trim() ? partToTranslate : `<tr><td>[等待内容...]</td></tr>`
                  };
                  if (!_.isEqual(streamingTranslatedStateObject[itemIdStr], newTableItem)) {
                      streamingTranslatedStateObject[itemIdStr] = newTableItem;
                  }
                  scheduleSetTranslated(streamingTranslatedStateObject);

                  const currentTableMapEntryIndexInTableItems = tableItems.findIndex(ti => String(ti.itemId) === itemIdStr);
                  if (currentTableMapEntryIndexInTableItems !== -1 && currentTableMapEntryIndexInTableItems + 1 < tableItems.length) {
                      currentTableProcessingIndex = translationMap.indexOf(tableItems[currentTableMapEntryIndexInTableItems + 1]);
                  } else {
                      currentTableProcessingIndex = -1;
                  }
                }
              }
              currentTranslatableMapIndex++;
            }
          }
        }

        // Update the last (potentially incomplete) part
        let tempMapIdx = currentTranslatableMapIndex;
        while (tempMapIdx < translationMap.length && !(translationMap[tempMapIdx].type === 'text' || translationMap[tempMapIdx].type === 'table')) {
            tempMapIdx++;
        }
        if (tempMapIdx < translationMap.length) {
            const mapEntry = translationMap[tempMapIdx];
            const itemId = mapEntry.itemId;
            const originalItem = mapEntry.originalItem;
            const itemIdStr = String(itemId);
            if (itemId !== undefined && originalItem) {
                if (mapEntry.type === 'text') {
                     const baseItem = initialStreamingStateObject[itemIdStr] || {id: itemId, tag: originalItem.tag, type: 'text'};
                     let textForLastPart = parseThinkContent(currentPartAccumulator).cleanContent; // Removed isThinkModelUsed condition
                     // if (isThinkModelUsed) {
                     //     textForLastPart = parseThinkContent(currentPartAccumulator).cleanContent;
                     // }
                     const newTextItem = {
                       ...baseItem,
                       children: textForLastPart,
                       type: 'text',
                       tag: originalItem.tag,
                       id: itemId
                     };
                     if (!_.isEqual(streamingTranslatedStateObject[itemIdStr], newTextItem)) {
                         streamingTranslatedStateObject[itemIdStr] = newTextItem;
                     }
                     scheduleSetTranslated(streamingTranslatedStateObject);
                }
            }
        }
        return true; // Continue stream
      };

      console.log(`[DEBUG Translation chunk ${chunkIndex}] About to call streamTextWithAI. UserMsg: "${userMessageContent.substring(0,100)}...", SystemPrompt: "${finalSystemPrompt.substring(0,100)}...", API: ${selectedApi?.provider}, Model: ${model}`);
      // Call the new streamTextWithAI utility
      const streamResult = await streamTextWithAI(
        userMessageContent,
        finalSystemPrompt,
        selectedApi, // This is the selectedApiConfig
        model,       // This is the modelName
        signal,
        handleStreamChunk,
        isViaProxy // Pass the isViaProxy flag
      );
      console.log(`[DEBUG Translation chunk ${chunkIndex}] streamTextWithAI call returned. Result:`, JSON.stringify(streamResult));

      if (!streamResult.success) {
        // Error handling is now centralized in streamTextWithAI,
        // which calls onChunk({ error: ..., isFinal: true })
        // and returns the error. We can re-throw or handle as needed.
        // The finally block will still execute.
        // If it was a CORS error, streamTextWithAI handles the retry internally.
        // If it's a non-CORS error after retries, it's passed here.
        throw new Error(streamResult.error || 'Streaming failed with an unknown error from streamTextWithAI.');
      }

      // The rest of the logic after the loop (table processing, final state update) remains largely the same,
      // as it operates on the fully populated streamingTranslatedStateObject.
      // Ensure rafHandle is cancelled if it was used by scheduleSetTranslated
      if (rafHandle) cancelAnimationFrame(rafHandle);

      // 流式处理完成后，如果还未标记完成，则标记 think 完成 (无条件)
      // （通常情况下，在有正式内容输出时就已经标记完成了）
      if (!hasStartedFormalContent) { // Removed isThinkModelUsed condition
        batchThinkUpdate(undefined, { isThinking: false, isCompleted: true });
        console.log(`[Translation] Chunk ${chunkIndex} stream ended, think completed (fallback)`);
      }

      // 清理Think更新定时器
      if (thinkUpdateTimeout) {
        clearTimeout(thinkUpdateTimeout);
      }

      for (const tableMapEntry of tableItems) {
        const tableItemId = tableMapEntry.itemId;
        const tableItemIdStr = String(tableItemId);
        if (tableItemId === undefined) continue;

        let tableHtml = tableAccumulators[tableItemIdStr] || '';
        try {
          if (tableHtml.trim() && !tableHtml.includes('<tr') && !tableHtml.includes('<td') && !tableHtml.includes('<th')) {
            const lines = tableHtml.split('\n').filter(line => line.trim());
            if (lines.length > 0) {
              let formattedContent = '';
              lines.forEach(line => {
                const cells = line.split('|').map(cell => cell.trim());
                formattedContent += '<tr>';
                if (cells.length > 1) cells.forEach(cell => { if (cell) formattedContent += `<td>${cell}</td>`; });
                else formattedContent += `<td>${line}</td>`;
                formattedContent += '</tr>';
              });
              tableHtml = formattedContent;
            } else {
              tableHtml = `<tr><td>${tableHtml}</td></tr>`;
            }
          } else if (!tableHtml.trim()) {
            tableHtml = '<tr><td>[空表格内容]</td></tr>';
          }
          const finalTableItem = { tag: 'table', id: tableItemId, children: tableHtml };
          if (!_.isEqual(streamingTranslatedStateObject[tableItemIdStr], finalTableItem)) {
              streamingTranslatedStateObject[tableItemIdStr] = finalTableItem;
          }
        } catch (e) {
          const errorTableItem = { tag: 'table', id: tableItemId, children: `<tr><td style="color:red; padding:10px; border:1px solid red;">表格处理错误: ${e.message}</td></tr>` };
          if (!_.isEqual(streamingTranslatedStateObject[tableItemIdStr], errorTableItem)) {
              streamingTranslatedStateObject[tableItemIdStr] = errorTableItem;
          }
        }
      }


      if (!hasBeenCancelled) {
        let newTranslated;
set(state => {
          newTranslated = { ...(state.translated || {}) };
          let changed = false;
          Object.keys(streamingTranslatedStateObject).forEach(key => {
              if(initialStreamingStateObject.hasOwnProperty(key)) {
                  if (!_.isEqual(newTranslated[key], streamingTranslatedStateObject[key])) {
                      newTranslated[key] = streamingTranslatedStateObject[key];
                      changed = true;
                  }
              }
          });
          // console.log(`[Store translateChunk] chunkIndex: ${chunkIndex}. Final stream update to store. Changed: ${changed}`);
          return changed ? { translated: newTranslated } : {};
        });

        // console.log(`[Store translateChunk] chunkIndex: ${chunkIndex}. Translation stream finished. Accumulated text length: ${accumulatedStreamedText.length}`);

        const hasNullsOrUndefined = translationMap.some(mapEntry => {
            if (mapEntry.type === 'passthrough' || mapEntry.type === 'img') return false;
            const itemId = mapEntry.itemId;
            if (itemId === undefined) return true;
            const translatedVal = streamingTranslatedStateObject[String(itemId)];
            return translatedVal === null || translatedVal === undefined;
        });
        const hasEmpty = translationMap.some(mapEntry => {
          if (mapEntry.type === 'passthrough' || mapEntry.type === 'img') return false;
          const itemId = mapEntry.itemId;
          if (itemId === undefined) return false;
          const ti = streamingTranslatedStateObject[String(itemId)];
          return ti && (
            (ti.type === 'text' && (ti.children === null || ti.children === undefined || String(ti.children).trim() === '')) ||
            (ti.tag === 'table' && (ti.children === null || ti.children === undefined || String(ti.children).trim() === ''))
          );
        });
        const finalStatus = (hasNullsOrUndefined || hasEmpty) ? 'partial_completed' : 'completed';
        // console.log(`[Store translateChunk] chunkIndex: ${chunkIndex}. Returning status: ${finalStatus}.`);

        return { status: finalStatus, chunkIndex };
      } else {
        // console.log(`[Store translateChunk] chunkIndex: ${chunkIndex}. Translation was cancelled. Restoring pre-translation state.`);
        const preStateForChunk = get().preTranslationChunkState.get(chunkIndex);
        if (preStateForChunk) {
            set(state => {
                const restoredTranslated = { ...(state.translated || {}) };
                let actualRestoreHappened = false;
                preStateForChunk.forEach((value, itemId) => {
                    if (!_.isEqual(restoredTranslated[itemId], value)) {
                        restoredTranslated[itemId] = value;
                        actualRestoreHappened = true;
                    }
                });
                return actualRestoreHappened ? { translated: restoredTranslated } : {};
            });
        }
        return { status: 'cancelled', wasCancelled: true };
      }

    } catch (error) {
      // translationError = error; // This variable was not declared, removing
      console.error(`[TranslationStore translateChunk] chunkIndex: ${chunkIndex}. Error during translation:`, error.name, error.message);
      if (rafHandle) cancelAnimationFrame(rafHandle);
      if (isAbortError(error)) {
        if (!hasBeenCancelled) {
            hasBeenCancelled = true;
             const preStateForChunk = get().preTranslationChunkState.get(chunkIndex);
             if (preStateForChunk) {
                 set(state => {
                     const restoredTranslated = { ...(state.translated || {}) };
                     let actualRestoreInCatch = false;
                     preStateForChunk.forEach((value, itemId) => {
                         if(!_.isEqual(restoredTranslated[itemId], value)) {
                           restoredTranslated[itemId] = value;
                           actualRestoreInCatch = true;
                         }
                     });
                     return actualRestoreInCatch ? { translated: restoredTranslated } : {};
                 });
             }
       }
       return { status: 'cancelled', wasCancelled: true, error: error };
     } else {
       const errorTranslatedStateObject = { ...(get().translated || {}) };
       let changedInErrorHandling = false;
       translationMap.forEach(mapEntry => {
         const itemId = mapEntry.itemId;
         const originalItem = mapEntry.originalItem;
         const itemIdStr = String(itemId);
         if (itemId !== undefined && originalItem) {
           if (mapEntry.type === 'text' || mapEntry.type === 'table') {
             const errorMessageContent = `[翻译失败: ${error.message || '未知API/流错误'}]`;
             let newErrorItem;
             const currentItemState = errorTranslatedStateObject[itemIdStr];

             if (currentItemState && currentItemState.type !== 'placeholder' && typeof currentItemState === 'object') {
               newErrorItem = { ...currentItemState };
               newErrorItem.children = (newErrorItem.children || '') +
                                       (newErrorItem.type === 'text' || !newErrorItem.children ?
                                         (newErrorItem.children ? '\n' : '') + errorMessageContent :
                                         `<p style="color:red;">${errorMessageContent}</p>`);
               newErrorItem.type = 'error';
             } else {
               newErrorItem = { id: itemId, tag: originalItem.tag, type: 'error', children: errorMessageContent };
             }
             if (!_.isEqual(errorTranslatedStateObject[itemIdStr], newErrorItem)) {
               errorTranslatedStateObject[itemIdStr] = newErrorItem;
               changedInErrorHandling = true;
             }
           }
         }
       });
       if (changedInErrorHandling) {
         set({ translated: errorTranslatedStateObject });
       }
       return { status: 'error', error: error };
     }
    } finally {
      // Roo: 标记此chunk的流已结束，无论成功与否
      const controllersMap = get().currentTranslationControllers;
      if (controllersMap.has(chunkIndex)) {
        const entry = controllersMap.get(chunkIndex);
        if (entry) { // Ensure entry exists
          // Create a new Map to ensure Zustand detects the change for reactivity
          const newControllersMap = new Map(controllersMap);
          newControllersMap.set(chunkIndex, { ...entry, controller: entry.controller, streamEnded: true });
          set(state => ({ ...state, currentTranslationControllers: newControllersMap }));
        }
      }

      if (rafHandle) cancelAnimationFrame(rafHandle);
      // Always remove controller and clear pre-translation state when this instance of translateChunk finishes.
      // If a retry occurred, the new translateChunk instance will manage its own controller.
      if (get().currentTranslationControllers.has(chunkIndex)) {
        removeTranslationController(chunkIndex);
      }
      clearPreTranslationChunkState(chunkIndex);

      // 注意：不清理 think 状态，让用户可以查看 think 过程
    }
  },
});

const createTranslationController = (_, get) => ({
  translateAll: async (chunked) => {
    get().setGlobalCancellationFlag(false);
    get().setIsTranslatingAllActive(true);

    const currentTranslated = get().translated || {};

    // if (chunked && chunked.length > 0) {
    //   chunked.forEach((chunk, chunkIndex) => {
    //     if (chunk && chunk.length > 0) {
    //       const chunkIds = chunk.map(item => item.id).filter(Boolean).map(String);
    //       const translatedIds = chunkIds.filter(id => currentTranslated[id] &&
    //         typeof currentTranslated[id] === 'object' &&
    //         currentTranslated[id].children &&
    //         typeof currentTranslated[id].children === 'string' &&
    //         currentTranslated[id].children.trim() !== '');
    //     }
    //   });
    // }

    const userConcurrencyLevel = get().user?.concurrencyLevel ?? 3;
    const { translateChunk } = get();
    const indicesToTranslate = [];

    const currentTranslatedObject = get().translated || {};
    for (let i = 0; i < chunked.length; i++) {
      const chunkItems = chunked[i];
      const needsTranslation = chunkItems.some(item => {
        if (!item || typeof item.id === 'undefined') return false;
        const itemIdStr = String(item.id);
        const translatedItem = currentTranslatedObject[itemIdStr];
        if (!translatedItem || translatedItem.type === 'error' || translatedItem.type === 'placeholder') return true;
        if (item.tag !== 'table' && translatedItem.type === 'text') return !translatedItem.children || String(translatedItem.children).trim() === '';
        if (item.tag === 'table' && translatedItem.tag === 'table') return !translatedItem.children || String(translatedItem.children).trim() === '';
        if (typeof translatedItem === 'object' && translatedItem !== null && Object.keys(translatedItem).length === 0 && item.tag !== 'img' ) return true;
        return false;
      });
      if (needsTranslation) indicesToTranslate.push(i);
    }

    get().setIndicesToTranslate(indicesToTranslate);
    if (indicesToTranslate.length === 0) {
      // console.log('[Store translateAll] No chunks need translation.');
      get().setIsTranslatingAllActive(false);
      get().clearIndicesToTranslate();
      return;
    }

    try {
      for (let i = 0; i < indicesToTranslate.length; i += userConcurrencyLevel) {
        if (get().isTranslationGloballyCancelled) {
          // console.log('[Store translateAll] Globally cancelled, breaking loop.');
          break;
        }
        const batchIndices = indicesToTranslate.slice(i, i + userConcurrencyLevel);
        // console.log(`[Store translateAll] Processing batch: [${batchIndices.join(', ')}]`);

        const batchPromises = batchIndices.map(index => {
          if (get().isTranslationGloballyCancelled) {
            return Promise.resolve({ status: 'cancelled_globally', chunkIndex: index });
          }
          if (get().currentTranslationControllers.has(index)) {
            return Promise.resolve({ status: 'skipped_already_active', chunkIndex: index });
          }
          // translateChunk 现在接受第四个参数 isViaProxy，默认为 false
          return translateChunk(chunked[index], index, 0, false)
            .catch(error => {
              console.error(`[Store translateAll] Error in translateChunk for index ${index}:`, error);
              return { status: 'error', error: error.message, chunkIndex: index };
            });
        });

        const results = await Promise.all(batchPromises);
        // console.log(`[Store translateAll] Batch [${batchIndices.join(', ')}] results:`, JSON.stringify(results.map(r => ({idx: r.chunkIndex, status: r.status}))));

        if (get().isTranslationGloballyCancelled) {
            // console.log('[Store translateAll] Globally cancelled after batch, breaking loop.');
            break;
        }
      }
    } catch (error) {
      console.error("[TranslationStore translateAll] Unexpected error in main loop:", error);
    } finally {
      // Perform a single save operation after all chunks are processed or loop is exited
      if (!get().isTranslationGloballyCancelled) {
        try {
          const finalTranslatedState = get().translated || {};
          const finalArticleDetails = get().article;
          const storeSaveArticle = get().saveArticle;
          const finalContent = get().content;

          if (storeSaveArticle && typeof storeSaveArticle === 'function') {
            // console.log(`[Store translateAll finally] Performing final save. Translated items: ${Object.keys(finalTranslatedState).length}`);
            const translatedCopyForFinalSave = JSON.parse(JSON.stringify(finalTranslatedState));
            await storeSaveArticle({
              title: finalArticleDetails?.title,
              translated: translatedCopyForFinalSave,
              content: finalContent
            });
            // console.log('[Store translateAll finally] Final save successful.');
          } else {
            console.warn('[TranslationStore translateAll finally] saveArticle function not found. Final save skipped.');
          }
        } catch (finalSaveError) {
          console.error('[TranslationStore translateAll finally] Error during final save:', finalSaveError);
        }
      } else {
        // console.log('[Store translateAll finally] Global cancellation was active, final save skipped.');
      }

      // console.log('[Store translateAll] Finalizing. isTranslatingAllActive: false.');
      get().setIsTranslatingAllActive(false);
      get().clearIndicesToTranslate();
    }
  },

  cancelSingleTranslation: (chunkIndex) => {
    const { currentTranslationControllers, removeTranslationController, clearStreamingChunkText, clearPreTranslationChunkState, clearChunkThinkContent, clearChunkThinkState } = get();
    const controllerEntry = currentTranslationControllers.get(chunkIndex);
    if (controllerEntry && controllerEntry.controller) {
      controllerEntry.controller.abort();
    }
    clearPreTranslationChunkState(chunkIndex);
    if (currentTranslationControllers.has(chunkIndex)) removeTranslationController(chunkIndex);
    clearStreamingChunkText(chunkIndex);
    // 清理 think 状态
    clearChunkThinkContent(chunkIndex);
    clearChunkThinkState(chunkIndex);
    return { status: 'cancelled', wasCancelled: true };
  },

  cancelAllTranslations: () => {
    const { currentTranslationControllers, clearAllControllers, clearAllStreamingTexts, setGlobalCancellationFlag, clearPreTranslationChunkState, setIsTranslatingAllActive, clearIndicesToTranslate, clearAllThinkStates } = get();

    setGlobalCancellationFlag(true);
    setIsTranslatingAllActive(false);

    let cancelledCount = 0;
    new Map(currentTranslationControllers).forEach((entry, chunkIndex) => {
      if (entry && entry.controller && typeof entry.controller.abort === 'function') {
        try {
          entry.controller.abort();
          cancelledCount++;
        } catch (e) {
          // ignore error
        }
        clearPreTranslationChunkState(chunkIndex);
      }
    });

    clearAllControllers();
    clearAllStreamingTexts();
    clearIndicesToTranslate();
    // 清理所有 think 状态
    clearAllThinkStates();

    return { cancelled: cancelledCount, status: 'cancelled_all', wasCancelled: true };
  }
});

const createTranslationEditor = (set, get) => ({
  updateTranslatedChunk: async (chunkItems, newContentArray) => {
    const currentTranslatedFromStore = get().translated || {};
    if (!chunkItems || chunkItems.length === 0) {
      console.error("[TranslationStore updateTranslatedChunk] Error: Invalid chunkItems (empty or null).");
      throw new Error("无效的 chunkItems");
    }

    const updatedTranslatedObjectForStore = { ...currentTranslatedFromStore };
    let changesMade = false;

    if (newContentArray.length === 0) {
      chunkItems.forEach(originalItem => {
        if (originalItem && typeof originalItem.id !== 'undefined') {
          const originalItemIdStr = String(originalItem.id);
          if (updatedTranslatedObjectForStore.hasOwnProperty(originalItemIdStr)) { // Check existence before delete/undefine
            updatedTranslatedObjectForStore[originalItemIdStr] = undefined;
            changesMade = true;
          }
        }
      });
    } else {
      chunkItems.forEach((originalItem, index) => {
        if (originalItem && typeof originalItem.id !== 'undefined') {
          const originalItemId = originalItem.id;
          const originalItemIdStr = String(originalItemId);
          if (index < newContentArray.length && newContentArray[index]) {
            const newTranslatedItemFromEditor = { ...newContentArray[index] };
            if (typeof newTranslatedItemFromEditor.id === 'undefined') {
                 newTranslatedItemFromEditor.id = originalItemId;
            } else if (String(newTranslatedItemFromEditor.id) !== originalItemIdStr) {
                newTranslatedItemFromEditor.id = originalItemId;
            }

            if (!_.isEqual(updatedTranslatedObjectForStore[originalItemIdStr], newTranslatedItemFromEditor)) {
              updatedTranslatedObjectForStore[originalItemIdStr] = newTranslatedItemFromEditor;
              changesMade = true;
            }
          } else {
            if (updatedTranslatedObjectForStore.hasOwnProperty(originalItemIdStr)) {
              updatedTranslatedObjectForStore[originalItemIdStr] = undefined;
              changesMade = true;
            }
          }
        }
      });
    }

    if (changesMade) {
      set({ translated: updatedTranslatedObjectForStore });
    }

    try {
      const currentArticleDetails = get().article;
      const currentContentFromStore = get().content;
      const storeSaveArticle = get().saveArticle;

      if (storeSaveArticle && typeof storeSaveArticle === 'function') {
        await storeSaveArticle({
          title: currentArticleDetails?.title,
          translated: updatedTranslatedObjectForStore,
          content: currentContentFromStore
        });
      } else {
        console.warn("[TranslationStore updateTranslatedChunk]: saveArticle function not found in store. Cannot save changes to backend.");
      }
    } catch (error) {
      console.error("[TranslationStore updateTranslatedChunk]: Error calling saveArticle:", error);
      throw error;
    }
  },

  clearTranslatedChunk: async (chunkItems) => {
    const currentTranslated = get().translated || {};
    if (!chunkItems || chunkItems.length === 0) {
      console.error("[TranslationStore clearTranslatedChunk] Invalid chunkItems.");
      throw new Error("无效的 chunkItems");
    }
    const updatedTranslatedForChunk = { ...currentTranslated };
    chunkItems.forEach(originalItem => {
      if (originalItem && typeof originalItem.id !== 'undefined') {
        updatedTranslatedForChunk[String(originalItem.id)] = undefined;
      }
    });
    set({ translated: updatedTranslatedForChunk });
    try {
      const currentArticleDetails = get().article;
      const storeSaveArticle = get().saveArticle;
      if (storeSaveArticle && typeof storeSaveArticle === 'function') {
        await storeSaveArticle({ title: currentArticleDetails?.title, translated: updatedTranslatedForChunk, content: get().content });
      } else {
        console.warn("[TranslationStore clearTranslatedChunk]: saveArticle function not found in store.");
      }
    } catch (error) {
      console.error("[TranslationStore clearTranslatedChunk]: Error saving article:", error);
      throw error;
    }
  },
});

// 术语统一相关的 Actions
const createTerminologySyncActions = (set, get) => ({
  openTerminologyModal: (termList) => set({
    isTerminologyModalOpen: true,
    terminologyListForModal: termList,
  }),
  closeTerminologyModal: () => set({
    isTerminologyModalOpen: false,
    terminologyListForModal: [],
    aiParsedTerminologyContext: new Map(), // 清空旧的上下文
    terminologyExtractionProgress: {
      isProcessing: false,
      currentStep: '',
      processedChunks: 0,
      totalChunks: 0,
      errors: []
    }
  }),
  updateTerminologyProgress: (progressUpdate) => {
    if (progressUpdate.errors && progressUpdate.errors.length > 0) {
      console.log(`[SYNC_LOG stores.translation.js:1156] updateTerminologyProgress with errors:`, JSON.stringify(progressUpdate.errors, null, 2));
    }
    set(state => ({
      terminologyExtractionProgress: {
        ...state.terminologyExtractionProgress,
        ...progressUpdate
      }
    }));
  },
  startTerminologySyncProcess: async (chunkedData = null) => {
    console.log(`[SYNC_LOG stores.translation.js:1162] startTerminologySyncProcess called. chunkedData.length: ${chunkedData?.length}. First chunk sample (first 3 items):`, JSON.stringify(chunkedData?.[0]?.slice(0,3), null, 2));
    // 立即打开modal并显示进度
    get().openTerminologyModal([]);
    get().updateTerminologyProgress({
      isProcessing: true,
      currentStep: '正在准备术语提取...',
      processedChunks: 0,
      totalChunks: 0,
      errors: []
    });

    const { translated, apis, defaultApiModel, getSelectedApiConfig } = get();
    const currentTranslated = translated || {};

    // 使用传入的chunkedData，如果没有传入则报错
    let chunksToProcess;
    if (chunkedData && Array.isArray(chunkedData) && chunkedData.length > 0) {
      // 验证chunkedData的格式是否正确
      if (Array.isArray(chunkedData[0])) {
        chunksToProcess = chunkedData;
      } else {
        console.error('[TerminologySync] chunkedData格式错误：应该是Array<Array<ItemObject>>，但收到Array<ItemObject>');
        get().updateTerminologyProgress({
          isProcessing: false,
          currentStep: '数据格式错误',
          errors: ['传入的chunk数据格式不正确']
        });
        return;
      }
    } else {
      console.error('[TerminologySync] 缺少chunkedData参数或数据为空');
      get().updateTerminologyProgress({
        isProcessing: false,
        currentStep: '缺少必要数据',
        errors: ['未提供chunk数据，请确保文章内容已正确加载']
      });
      return;
    }

    let hasAnyValidChunk = false;
    if (Array.isArray(chunksToProcess)) {
        for (const ch of chunksToProcess) {
            // A valid chunk is an array with at least one item object.
            if (Array.isArray(ch) && ch.length > 0 && ch.every(item => item && typeof item === 'object')) {
                hasAnyValidChunk = true;
                break;
            }
        }
    }

    if (!hasAnyValidChunk) {
      get().updateTerminologyProgress({
        isProcessing: false,
        currentStep: '未找到有效的翻译内容',
        errors: ['没有可用于术语提取的翻译内容']
      });
      return;
    }

    const apiConfigResult = getSelectedApiConfig(apis, defaultApiModel);
    const selectedApiConfig = apiConfigResult.selectedApiConfig;
    const modelName = apiConfigResult.modelName;

    if (!selectedApiConfig || !modelName) {
      get().updateTerminologyProgress({
        isProcessing: false,
        currentStep: 'API配置错误',
        errors: ['未找到有效的API配置或模型']
      });
      return;
    }

    // --- Roo: 预计算有效文本块数量 START ---
    get().updateTerminologyProgress({
      currentStep: '正在计算有效文本块数量...',
      processedChunks: 0,
      totalChunks: chunksToProcess.length, // Initial estimate
      errors: []
    });

    let effectiveTotalChunks = 0;
    const tempCurrentTranslated = get().translated || {}; // Get currentTranslated once for efficiency

    for (let i = 0; i < chunksToProcess.length; i++) {
      const chunk = chunksToProcess[i];
      if (!Array.isArray(chunk) || chunk.length === 0) {
        continue;
      }

      let originalChunkText = "";
      let translatedChunkText = "";
      const validOriginalItems = [];
      const validTranslatedItems = [];

      chunk.forEach(item => {
        if (item && item.type !== 'img' && item.children && typeof item.children === 'string') {
          const translatedItem = tempCurrentTranslated[String(item.id)];
          if (translatedItem && translatedItem.children && typeof translatedItem.children === 'string' &&
              (translatedItem.type === 'text' || (translatedItem.type === undefined && translatedItem.tag !== 'img'))) {
            validOriginalItems.push(item);
            validTranslatedItems.push(translatedItem);
          }
        }
      });

      validOriginalItems.forEach(item => {
        originalChunkText += item.children + "\n";
      });
      validTranslatedItems.forEach(item => {
        translatedChunkText += item.children + "\n";
      });

      originalChunkText = originalChunkText.trim();
      translatedChunkText = translatedChunkText.trim();

      if (originalChunkText && translatedChunkText) {
        effectiveTotalChunks++;
      }
    }
    console.log('[TerminologySync stores.translation.js:Roo] Calculated effectiveTotalChunks:', effectiveTotalChunks);
    // --- Roo: 预计算有效文本块数量 END ---

    // 更新总chunk数量为有效数量
    get().updateTerminologyProgress({
      currentStep: '开始提取术语...',
      totalChunks: effectiveTotalChunks // Roo: 使用有效块数量
    });

    // 获取用户设置的并发数
    const userConcurrencyLevel = get().user?.concurrencyLevel ?? 3;

    // 使用并行处理，按批次处理以控制并发数并显示进度
    const perChunkAiResults = [];

    // 创建处理单个chunk的函数
    const processChunk = async (chunk, chunkIndex) => {
      console.log(`[SYNC_LOG stores.translation.js:1247] processChunk called for chunkIndex: ${chunkIndex}. Chunk length: ${chunk?.length}`);
      if (!Array.isArray(chunk) || chunk.length === 0) {
        console.log(`[SYNC_LOG stores.translation.js:1249] processChunk chunkIndex: ${chunkIndex} - Invalid or empty chunk.`);
        return { chunkIndex, terms: [], error: 'Invalid or empty chunk', sourceItemIds: [] };
      }

      let originalChunkText = "";
      let translatedChunkText = "";

      // 确保原文和译文的正确对应
      const validOriginalItems = [];
      const validTranslatedItems = []; // Will store the actual translated item objects
      const sourceItemIdsForChunk = []; // Will store the IDs of the translated items

      chunk.forEach(item => {
        if (item && item.type !== 'img' && item.children && typeof item.children === 'string') {
          const translatedItem = currentTranslated[String(item.id)];
          if (translatedItem && translatedItem.children && typeof translatedItem.children === 'string' &&
              (translatedItem.type === 'text' || (translatedItem.type === undefined && translatedItem.tag !== 'img'))) {
            validOriginalItems.push(item);
            validTranslatedItems.push(translatedItem); // Store the full translated item
            sourceItemIdsForChunk.push(String(item.id)); // Store the ID
          }
        }
      });
      console.log(`[SYNC_LOG stores.translation.js:1270] processChunk chunkIndex: ${chunkIndex}. validOriginalItems: ${validOriginalItems.length}, validTranslatedItems: ${validTranslatedItems.length}, sourceItemIdsForChunk: ${sourceItemIdsForChunk.join(',')}`);

      // 构建对应的原文和译文文本
      validOriginalItems.forEach(item => {
        originalChunkText += item.children + "\n";
      });

      validTranslatedItems.forEach(item => { // Iterate over stored translated items
        translatedChunkText += item.children + "\n";
      });

      originalChunkText = originalChunkText.trim();
      translatedChunkText = translatedChunkText.trim();

      if (!originalChunkText || !translatedChunkText) {
        console.log(`[SYNC_LOG stores.translation.js:1284] processChunk chunkIndex: ${chunkIndex} - Missing original or translated text after filtering. Original length: ${originalChunkText.length}, Translated length: ${translatedChunkText.length}`);
        
        const isChunkIntentionallySkipped = chunk.every(item => item.tag === 'img' /* || item.tag === 'hr' || etc. */);

        if (isChunkIntentionallySkipped) {
          console.log(`[SYNC_LOG stores.translation.js:Roo_SKIP] processChunk chunkIndex: ${chunkIndex} - Skipped as it likely contains only images or non-textual content.`);
          return { chunkIndex, originalChunkContent: originalChunkText, translatedChunkContent: translatedChunkText, terms: [], skippedAsEmpty: true, error: null, sourceItemIds: sourceItemIdsForChunk };
        } else {
          return { chunkIndex, originalChunkContent: originalChunkText, translatedChunkContent: translatedChunkText, terms: [], error: 'Missing original or translated text after filtering valid items (and not identified as skippable content)', sourceItemIds: sourceItemIdsForChunk };
        }
      }
      console.log(`[SYNC_LOG stores.translation.js:1287] processChunk chunkIndex: ${chunkIndex}. Original text to send (first 100 chars): "${originalChunkText.substring(0,100)}". Translated text (first 100 chars): "${translatedChunkText.substring(0,100)}"`);

      const singleChunkSystemPrompt = `You are an expert in terminology extraction. Given an original text segment and its translation, identify key original language terms and their corresponding translations within this specific segment.

IMPORTANT GUIDELINES:
1. **Case-insensitive matching**: Treat terms as the same regardless of case (e.g., "Paclitaxel", "paclitaxel", "PACLITAXEL" are the same term)
2. **Multi-word terms**: Include compound terms, phrases, and multi-word expressions (e.g., "overall survival", "progression-free survival", "adverse events")
3. **Technical terminology**: Focus on domain-specific terms, proper nouns, drug names, medical terms, technical concepts
4. **Consistent formatting**: Use the most common capitalization form found in the text for the originalTerm

Output the result as a JSON array of objects. Each object should have the following structure:
{
  "originalTerm": "The original term found in the original segment (use most common capitalization)",
  "translationInThisChunk": "The translation of this term found in the translated segment"
}

Focus on accuracy. Only include terms that are present in both the original and translated segments.
If a term appears multiple times in the original segment with the same translation in the translated segment, list it only once.
Do not include terms if they are not clearly translated or if the translation is ambiguous in the provided translated segment.
Include both single words and multi-word phrases that represent important concepts.
Ensure the output is a valid JSON array. If no terms are found, return an empty array [].
`;

      const userPromptForChunk = `Original Text Segment:
---
${originalChunkText}
---

Translated Text Segment:
---
${translatedChunkText}
---
`;

      try {
        console.log(`[SYNC_LOG stores.translation.js:1317] processChunk chunkIndex: ${chunkIndex} - Calling formatTextWithAI. Model: ${modelName}`);
        const aiResult = await formatTextWithAI(
          userPromptForChunk,
          singleChunkSystemPrompt,
          selectedApiConfig,
          modelName,
          null, // AbortSignal
          1,    // Concurrency for formatTextWithAI's internal chunking (disabled)
          true  // disableChunking for formatTextWithAI
        );
        console.log(`[SYNC_LOG stores.translation.js:1328] processChunk chunkIndex: ${chunkIndex} - formatTextWithAI result (error: ${aiResult.error}, text length: ${aiResult.formattedText?.length}):`, aiResult.formattedText?.substring(0,100));

        if (aiResult.error || !aiResult.formattedText) {
          console.error(`[SYNC_LOG stores.translation.js:1331] processChunk chunkIndex: ${chunkIndex} - AI Error or no formatted text: ${aiResult.error}`);
          return { chunkIndex, originalChunkContent: originalChunkText, translatedChunkContent: translatedChunkText, terms: [], error: aiResult.error || 'No formatted text from AI', sourceItemIds: sourceItemIdsForChunk };
        }

        let parsedChunkTerms = [];
        try {
          let jsonString = aiResult.formattedText.trim();
          console.log(`[SYNC_LOG stores.translation.js:1337] processChunk chunkIndex: ${chunkIndex} - Raw AI JSON string (first 100): ${jsonString.substring(0,100)}`);
          if (jsonString.startsWith("```json")) jsonString = jsonString.substring(7);
          if (jsonString.endsWith("```")) jsonString = jsonString.substring(0, jsonString.length - 3);
          jsonString = jsonString.trim();
          console.log(`[SYNC_LOG stores.translation.js:1341] processChunk chunkIndex: ${chunkIndex} - Cleaned AI JSON string (first 100): ${jsonString.substring(0,100)}`);

          parsedChunkTerms = JSON.parse(jsonString);
          console.log(`[SYNC_LOG stores.translation.js:1344] processChunk chunkIndex: ${chunkIndex} - Parsed terms count: ${parsedChunkTerms?.length}. First term:`, JSON.stringify(parsedChunkTerms?.[0]));


          if (!Array.isArray(parsedChunkTerms)) {
            console.error(`[SYNC_LOG stores.translation.js:1348] processChunk chunkIndex: ${chunkIndex} - AI response is not a JSON array after parsing.`);
            throw new Error("AI response for chunk is not a JSON array.");
          }
           if (parsedChunkTerms.some(term => typeof term.originalTerm !== 'string' || typeof term.translationInThisChunk !== 'string')) {
            console.error(`[SYNC_LOG stores.translation.js:1352] processChunk chunkIndex: ${chunkIndex} - Invalid term structure in AI response.`);
            throw new Error("Invalid term structure in AI response for chunk.");
          }
        } catch (e) {
          console.error(`[SYNC_LOG stores.translation.js:1356] processChunk chunkIndex: ${chunkIndex} - JSON Parsing Error: ${e.message}. Original AI text: ${aiResult.formattedText}`);
          return { chunkIndex, originalChunkContent: originalChunkText, translatedChunkContent: translatedChunkText, terms: [], error: `JSON Parsing Error: ${e.message}`, sourceItemIds: sourceItemIdsForChunk };
        }
        console.log(`[SYNC_LOG stores.translation.js:1360] processChunk chunkIndex: ${chunkIndex} - Successfully processed. Terms found: ${parsedChunkTerms.length}`);
        return { chunkIndex, originalChunkContent: originalChunkText, translatedChunkContent: translatedChunkText, terms: parsedChunkTerms, sourceItemIds: sourceItemIdsForChunk };

      } catch (error) {
        console.error(`[SYNC_LOG stores.translation.js:1364] processChunk chunkIndex: ${chunkIndex} - General Error in processChunk: ${error.message}`);
        return { chunkIndex, originalChunkContent: originalChunkText, translatedChunkContent: translatedChunkText, terms: [], error: error.message, sourceItemIds: sourceItemIdsForChunk };
      }
    };

    // 使用批次并行处理，控制并发数并按单个chunk显示进度
    let processedCount = 0;

    for (let i = 0; i < chunksToProcess.length; i += userConcurrencyLevel) {
      const batchIndices = [];
      for (let j = i; j < Math.min(i + userConcurrencyLevel, chunksToProcess.length); j++) {
        batchIndices.push(j);
      }
      console.log(`[SYNC_LOG stores.translation.js:1370] Processing batch starting with index ${i}. Batch indices: ${batchIndices.join(', ')}`);

      // 创建包装的processChunk函数，用于实时更新进度
      const processChunkWithProgress = async (chunk, chunkIndex) => {
        console.log(`[SYNC_LOG stores.translation.js:1375] processChunkWithProgress called for chunkIndex: ${chunkIndex}`);
        const result = await processChunk(chunk, chunkIndex);
        console.log(`[SYNC_LOG stores.translation.js:1377] processChunkWithProgress chunkIndex: ${chunkIndex} - result: terms: ${result?.terms?.length}, error: ${result?.error}`);

        // 每完成一个chunk就更新进度
        // Roo: The outer scope's `processedCount` should not be incremented or used directly here for UI state if `totalChunks` is effective.
        // Roo: Manage UI progress based on effectively processed chunks.
        const wasEffectivelyProcessed = !result.skippedAsEmpty && (!result.error || (result.terms && result.terms.length > 0));
        const oldProgress = get().terminologyExtractionProgress;
        // Increment processedChunks state only if the chunk was effectively processed
        const newProcessedChunksForUI = wasEffectivelyProcessed ? oldProgress.processedChunks + 1 : oldProgress.processedChunks;
        const totalChunksForUI = oldProgress.totalChunks; // This is already effectiveTotalChunks

        console.log(`[SYNC_LOG stores.translation.js:Roo_PROGRESS_UPDATE] Chunk ${chunkIndex}. Effective: ${wasEffectivelyProcessed}. UI Processed: ${newProcessedChunksForUI}/${totalChunksForUI}. Error: ${result.error}, SkippedEmpty: ${result.skippedAsEmpty}`);
        
        get().updateTerminologyProgress({
          currentStep: `正在处理第 ${newProcessedChunksForUI}/${totalChunksForUI} 个文本段...`, // Numerator and Denominator now consistent
          processedChunks: newProcessedChunksForUI // This state field now tracks effectively processed chunks
        });

        // 如果有错误，添加到错误列表
        if (result.error) {
          const currentProgress = get().terminologyExtractionProgress;
          console.log(`[SYNC_LOG stores.translation.js:1390] Error in chunk ${result.chunkIndex + 1}: ${result.error}. Adding to progress.errors.`);
          get().updateTerminologyProgress({
            errors: [...currentProgress.errors, `Chunk ${result.chunkIndex + 1}: ${result.error}`]
          });
        }

        return result;
      };

      // 并行处理当前批次
      const batchPromises = batchIndices.map(chunkIndex =>
        processChunkWithProgress(chunksToProcess[chunkIndex], chunkIndex)
      );

      const batchResults = await Promise.all(batchPromises);

      // 收集结果
      batchResults.forEach(result => {
        perChunkAiResults.push(result);
      });
      console.log(`[SYNC_LOG stores.translation.js:1403] Batch starting with index ${i} completed. perChunkAiResults length: ${perChunkAiResults.length}`);
    }

    // 更新进度：开始聚合术语
    get().updateTerminologyProgress({
      currentStep: '正在聚合和分析术语...',
      processedChunks: chunksToProcess.length // This should be the total number of chunks attempted
    });
    console.log(`[SYNC_LOG stores.translation.js:1411] All chunks processed. Starting aggregation. perChunkAiResults (first 5):`, JSON.stringify(perChunkAiResults.slice(0,5).map(r => ({idx: r.chunkIndex, terms: r.terms?.length, error: r.error})), null, 2));


    // 2. Aggregate and Compare (JavaScript logic with case-insensitive term matching)
    const aggregatedTerms = new Map(); // Map<string (lowercaseOriginalTerm), { originalTerm: string, variants: Array<{ chunkIndex: number, translationVariant: string, initialTranslationVariant: string, originalChunkContent: string, translatedChunkContent: string, sourceItemIds: string[] }> }>
    const termCaseMapping = new Map(); // Map<string (lowercaseOriginalTerm), string (preferredCaseOriginalTerm)> - to track the preferred capitalization

    perChunkAiResults.forEach(chunkResult => {
      if (!chunkResult) {
        console.warn(`[SYNC_LOG stores.translation.js:1420] Encountered undefined chunkResult during aggregation.`);
        return;
      }
      if (chunkResult.error || !chunkResult.terms) {
        console.log(`[SYNC_LOG stores.translation.js:1424] Skipping chunkIndex ${chunkResult.chunkIndex} due to error or no terms. Error: ${chunkResult.error}`);
        return;
      }

      chunkResult.terms.forEach(termPair => {
        const { originalTerm, translationInThisChunk } = termPair;
        if (!originalTerm || !translationInThisChunk) return; // Skip incomplete term pairs

        const lowercaseOriginalTerm = originalTerm.toLowerCase();

        const findCorrectCaseInOriginalText = (term, originalText) => {
          const escapedTerm = _.escapeRegExp(term);
          const patterns = [ `\\b${escapedTerm}\\b`, `${escapedTerm}`, escapedTerm.replace(/[-\s]/g, '[-\\s]*')];
          for (const pattern of patterns) {
            try {
              const regex = new RegExp(pattern, 'gi');
              const matches = originalText.match(regex);
              if (matches && matches.length > 0) {
                console.log(`[TerminologySync] Found term "${term}" in original text using pattern "${pattern}": "${matches[0]}"`);
                return matches[0];
              }
            } catch (e) {
              console.warn(`[TerminologySync] Invalid regex pattern "${pattern}" for term "${term}":`, e.message);
              continue;
            }
          }
          console.warn(`[TerminologySync] Term "${term}" not found in original text: "${originalText.substring(0, 100)}..."`);
          return term;
        };

        const correctCaseFromOriginal = findCorrectCaseInOriginalText(originalTerm, chunkResult.originalChunkContent);

        if (!termCaseMapping.has(lowercaseOriginalTerm)) {
          termCaseMapping.set(lowercaseOriginalTerm, correctCaseFromOriginal);
        } else {
          const currentPreferred = termCaseMapping.get(lowercaseOriginalTerm);
          const isCurrentFromOriginalExact = correctCaseFromOriginal !== originalTerm;
          const hasCapitalLetter = /[A-Z]/.test(correctCaseFromOriginal);
          const currentHasCapital = /[A-Z]/.test(currentPreferred);
          const isAllCaps = correctCaseFromOriginal === correctCaseFromOriginal.toUpperCase() && correctCaseFromOriginal !== correctCaseFromOriginal.toLowerCase();
          const currentIsAllCaps = currentPreferred === currentPreferred.toUpperCase() && currentPreferred !== currentPreferred.toLowerCase();
          if (isCurrentFromOriginalExact || (hasCapitalLetter && !isAllCaps && (!currentHasCapital || currentIsAllCaps))) {
            termCaseMapping.set(lowercaseOriginalTerm, correctCaseFromOriginal);
          }
        }

        if (!aggregatedTerms.has(lowercaseOriginalTerm)) {
          aggregatedTerms.set(lowercaseOriginalTerm, {
            originalTerm: termCaseMapping.get(lowercaseOriginalTerm),
            variants: []
          });
        }
        const existingTermEntry = aggregatedTerms.get(lowercaseOriginalTerm);
        existingTermEntry.originalTerm = termCaseMapping.get(lowercaseOriginalTerm);

        const newVariant = {
            chunkIndex: chunkResult.chunkIndex,
            translationVariant: translationInThisChunk, // This will be the user-editable version in modal
            initialTranslationVariant: translationInThisChunk, // This is the original translation from AI for this chunk
            originalChunkContent: chunkResult.originalChunkContent,
            translatedChunkContent: chunkResult.translatedChunkContent,
            sourceItemIds: chunkResult.sourceItemIds || [] // Ensure sourceItemIds is passed
        };
        existingTermEntry.variants.push(newVariant);
      });
    });

    const inconsistentTermsForModal = [];
    aggregatedTerms.forEach((termData, lowercaseOriginalTerm) => {
      const translationGroups = new Map();
      termData.variants.forEach(variant => {
        const normalizedTranslation = variant.translationVariant.trim().toLowerCase();
        if (!translationGroups.has(normalizedTranslation)) {
          translationGroups.set(normalizedTranslation, []);
        }
        translationGroups.get(normalizedTranslation).push(variant);
      });

      console.log(`[SYNC_LOG stores.translation.js:1533_DEBUG] Term: "${lowercaseOriginalTerm}", Preferred Case: "${termData.originalTerm}", Translation Groups:`, Array.from(translationGroups.keys()));

      if (translationGroups.size > 1) {
        translationGroups.forEach((variants) => {
          const representativeTranslation = variants[0].translationVariant;
          inconsistentTermsForModal.push({
            originalTerm: termData.originalTerm,
            variantsInChunks: variants.map(v => ({
              chunkIndex: v.chunkIndex,
              translationVariant: v.translationVariant, // User-editable
              initialTranslationVariant: v.initialTranslationVariant, // Original from this chunk
              originalChunkContentSnippet: generateContextSnippet(v.originalChunkContent, termData.originalTerm, 200),
              translatedChunkContentSnippet: generateContextSnippet(v.translatedChunkContent, v.translationVariant, 200),
              originalChunkContent: v.originalChunkContent,
              translatedChunkContent: v.translatedChunkContent,
              sourceItemIds: v.sourceItemIds // Pass sourceItemIds to modal/apply
            })),
            userDefinedStandardTranslation: representativeTranslation,
          });
        });
      }
    });
    console.log(`[SYNC_LOG stores.translation.js:1537] Aggregation complete. aggregatedTerms size: ${aggregatedTerms.size}. inconsistentTermsForModal length: ${inconsistentTermsForModal.length}. First inconsistent term (if any):`, JSON.stringify(inconsistentTermsForModal[0], (key, value) => key === 'originalChunkContent' || key === 'translatedChunkContent' || key === 'sourceItemIds' ? (value?.length > 50 ? (value.slice(0,50) + '...') : value) : value, 2));
    
    const finalErrors = get().terminologyExtractionProgress.errors;
    if (inconsistentTermsForModal.length === 0 && finalErrors && finalErrors.length > 0) {
      get().updateTerminologyProgress({
        isProcessing: false,
        currentStep: '术语提取完成，但存在错误',
      });
      console.log(`[SYNC_LOG stores.translation.js:1544] Final status: No inconsistent terms, but errors exist. Errors:`, JSON.stringify(finalErrors));
    } else if (inconsistentTermsForModal.length === 0) {
      get().updateTerminologyProgress({
        isProcessing: false,
        currentStep: '未发现需要同步的术语差异',
      });
      console.log(`[SYNC_LOG stores.translation.js:1550] Final status: No inconsistent terms, no errors.`);
    } else {
      get().updateTerminologyProgress({
        isProcessing: false,
        currentStep: `发现 ${inconsistentTermsForModal.length} 个需要同步的术语`,
      });
      console.log(`[SYNC_LOG stores.translation.js:1556] Final status: Found ${inconsistentTermsForModal.length} inconsistent terms.`);
    }

    // 更新术语列表，但保持modal打开状态
    set({
      terminologyListForModal: inconsistentTermsForModal
    });
    console.log(`[SYNC_LOG stores.translation.js:1562] Terminology list set for modal. Length: ${inconsistentTermsForModal.length}`);
  },
  applyTerminologySync: (confirmedTerms) => {
    const currentTranslated = get().translated || {};
    const newTranslated = _.cloneDeep(currentTranslated); // 深拷贝以避免直接修改原始状态
    let changesMade = false;

    if (!confirmedTerms || confirmedTerms.length === 0) {
      get().closeTerminologyModal();
      return;
    }
    console.log("[TerminologySync Apply] Starting. Confirmed terms:", JSON.stringify(confirmedTerms.map(t => ({ ot: t.originalTerm, vCount: t.variantsInChunks.length, firstV: t.variantsInChunks[0] })), (key, value) => (key === 'sourceItemIds' && value?.length > 5) ? (value.slice(0,5) + '...') : value, 2));


    // Iterate through confirmed terms from the modal
    confirmedTerms.forEach(termInfo => {
      // const { userDefinedStandardTranslation } = termInfo; // This might not be directly used if each variant dictates its own replacement

      termInfo.variantsInChunks.forEach(variant => {
        const textToSearchInDocument = variant.initialTranslationVariant; // The original translation of this specific instance
        const textToReplaceWith = variant.translationVariant;         // The user-confirmed/edited translation for this specific instance
        const sourceItemIds = variant.sourceItemIds;                  // The specific item IDs where this instance was found

        if (typeof textToSearchInDocument !== 'string' || typeof textToReplaceWith !== 'string') {
          console.warn(`[TerminologySync Apply] Skipping variant for original term "${termInfo.originalTerm}" due to invalid textToSearchInDocument or textToReplaceWith. Search: ${JSON.stringify(textToSearchInDocument)}, ReplaceWith: ${JSON.stringify(textToReplaceWith)}`, variant);
          return; // Skip this variant
        }

        if (!sourceItemIds || sourceItemIds.length === 0) {
          console.warn(`[TerminologySync Apply] Skipping variant for original term "${termInfo.originalTerm}" (search: "${textToSearchInDocument}") because sourceItemIds is missing or empty.`, variant);
          return; // Skip if no source items to target
        }

        const areEffectivelyDifferent = textToSearchInDocument.toLowerCase() !== textToReplaceWith.toLowerCase();
        console.log(`[TerminologySync Apply] Processing Variant: OriginalTerm: "${termInfo.originalTerm}", InitialInDoc: "${textToSearchInDocument}", ReplaceWithModal: "${textToReplaceWith}", SourceItemIds: [${sourceItemIds.join(', ')}], Different: ${areEffectivelyDifferent}`);

        if (areEffectivelyDifferent) {
          sourceItemIds.forEach(itemId => {
            const docItemToUpdate = newTranslated[itemId];

            if (docItemToUpdate && typeof docItemToUpdate.children === 'string' && docItemToUpdate.children.trim() !== '') {
              let currentItemText = docItemToUpdate.children;
              let originalItemTextSnapshot = currentItemText; // For comparison after replacement

              const escapedTextToSearch = _.escapeRegExp(textToSearchInDocument);
              const patterns = [
                `\\b${escapedTextToSearch}\\b`,
                `${escapedTextToSearch}`,
              ];
              let replacementMadeInThisItem = false;

              for (const pattern of patterns) {
                try {
                  const regex = new RegExp(pattern, 'gi'); // Global and case-insensitive for testing if ANY instance exists
                  if (regex.test(currentItemText)) {
                    // Create a new regex for replacement to avoid issues with lastIndex
                    // Roo: Changed 'gi' to 'i' to replace only the FIRST match, case-insensitively.
                    const replaceRegex = new RegExp(pattern, 'i');
                    currentItemText = currentItemText.replace(replaceRegex, textToReplaceWith);
                    replacementMadeInThisItem = true;
                    console.log(`[TerminologySync Apply] In ItemID ${itemId}: Replaced "${textToSearchInDocument}" with "${textToReplaceWith}" using pattern "${pattern}".`);
                    break;
                  }
                } catch (e) {
                  console.warn(`[TerminologySync Apply] In ItemID ${itemId}: Invalid regex pattern "${pattern}" for text "${textToSearchInDocument}":`, e.message);
                  continue;
                }
              }

              if (replacementMadeInThisItem && originalItemTextSnapshot !== currentItemText) {
                newTranslated[itemId].children = currentItemText;
                changesMade = true;
                console.log(`[TerminologySync Apply] ItemID ${itemId} updated. Before: "${originalItemTextSnapshot.substring(0,50)}...", After: "${currentItemText.substring(0,50)}..."`);
              } else if (replacementMadeInThisItem && originalItemTextSnapshot === currentItemText) {
                console.log(`[TerminologySync Apply] ItemID ${itemId}: Replacement attempt made for "${textToSearchInDocument}" with "${textToReplaceWith}", but item content did not change. This might indicate a case-only change that RegExp handled identically, or an edge case.`);
              } else if (!replacementMadeInThisItem) {
                console.warn(`[TerminologySync Apply] In ItemID ${itemId}: Failed to find and replace "${textToSearchInDocument}" with "${textToReplaceWith}". Item text (first 100): "${docItemToUpdate.children.substring(0, 100)}..."`);
              }
            } else {
              console.warn(`[TerminologySync Apply] ItemID ${itemId} (from sourceItemIds for "${textToSearchInDocument}") not found in newTranslated, or its children property is not a non-empty string.`);
            }
          });
        } else {
          console.log(`[TerminologySync Apply] No replacement needed for variant of "${termInfo.originalTerm}" (search: "${textToSearchInDocument}") as it's same as replaceWith "${textToReplaceWith}" (case-insensitive).`);
        }
      });
    });
    if (changesMade) {
      set({ translated: newTranslated });
      // 调用文章保存功能
      if (typeof get().saveArticle === 'function') {
        get().saveArticle({ translated: newTranslated })
          .then(() => {
            // console.log('[TerminologySync] Auto-save successful after terminology sync.');
          })
          .catch(error => {
            console.error('[TerminologySync] Auto-save failed after terminology sync:', error);
            // 可以在这里添加用户提示，例如使用 Ant Design message
            // import { message } from 'antd'; // 需要在文件顶部导入
            // message.error('术语统一后自动保存文章失败，请尝试手动保存。');
          });
      } else {
        // console.warn('[TerminologySync] saveArticle function not found in store. Auto-save skipped.');
      }

      // 实际应用中应使用更友好的通知，如 Ant Design message
      // import { message } from 'antd';
      // message.success('术语已成功同步到译文！');
      // alert('术语已成功同步到译文！请记得手动保存文章以持久化更改。'); // Removed alert
    } else {
      // console.log('[TerminologySync] No actual changes made to translations.');
      // alert('未检测到需要同步的术语更改，或所选标准译法与原文已一致。'); // Removed alert
    }

    get().closeTerminologyModal();
  },
});

export const createTranslationStore = (set, get) => ({
  ...translationState,
  ...createTranslationStateManagement(set, get),
  ...createTranslationService(set, get),
  ...createTranslationController(set, get),
  ...createTranslationEditor(set, get),
  ...createTerminologySyncActions(set, get), // 添加新的 actions
});
